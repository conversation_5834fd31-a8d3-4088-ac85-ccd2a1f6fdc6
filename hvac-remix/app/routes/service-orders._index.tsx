import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSearchParams, Link } from "@remix-run/react";
import * as React from "react";
import { useState } from "react";
import { Badge } from "~/components/ui/badge";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import {
  getServiceOrders,
  getServiceOrderStats,
  type ServiceOrderWithRelations,
  type ServiceOrderSearchFilters
} from "~/models/service-order.server";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);

  // Get pagination parameters from URL
  const page = parseInt(url.searchParams.get("page") || "1", 10);
  const pageSize = parseInt(url.searchParams.get("pageSize") || "10", 10);
  const orderBy = url.searchParams.get("orderBy") || "createdAt";
  const orderDirection = (url.searchParams.get("orderDirection") || "desc") as "asc" | "desc";

  // Get filter parameters from URL
  const search = url.searchParams.get("search") || undefined;
  const status = url.searchParams.get("status") || undefined;
  const priority = url.searchParams.get("priority") || undefined;
  const type = url.searchParams.get("type") || undefined;
  const customerId = url.searchParams.get("customerId") || undefined;
  const deviceId = url.searchParams.get("deviceId") || undefined;
  const dateFrom = url.searchParams.get("dateFrom") || undefined;
  const dateTo = url.searchParams.get("dateTo") || undefined;

  // Get service orders with pagination and filters
  const serviceOrdersResponse = await getServiceOrders(
    userId,
    { page, pageSize, orderBy, orderDirection },
    { search, status, priority, type, customerId, deviceId, dateFrom, dateTo }
  );

  return json({
    serviceOrders: serviceOrdersResponse.data || [],
    totalCount: serviceOrdersResponse.totalCount,
    totalPages: serviceOrdersResponse.totalPages,
    currentPage: serviceOrdersResponse.currentPage,
    error: serviceOrdersResponse.error,
  });
}

// Polish translations for Service Orders
const translations = {
  serviceOrders: 'Zlecenia Serwisowe',
  createServiceOrder: 'Utwórz Zlecenie Serwisowe',
  search: 'Szukaj',
  searchPlaceholder: 'Szukaj zleceń serwisowych...',
  status: 'Status',
  priority: 'Priorytet',
  type: 'Typ',
  all: 'Wszystkie',
  pending: 'Oczekujące',
  inProgress: 'W Trakcie',
  completed: 'Zakończone',
  cancelled: 'Anulowane',
  scheduled: 'Zaplanowane',
  waitingParts: 'Oczekiwanie na Części',
  qualityCheck: 'Kontrola Jakości',
  billed: 'Rozliczone',
  low: 'Niski',
  medium: 'Średni',
  high: 'Wysoki',
  urgent: 'Pilny',
  emergency: 'Awaryjny',
  maintenance: 'Konserwacja',
  repair: 'Naprawa',
  installation: 'Instalacja',
  inspection: 'Przegląd',
  warranty: 'Gwarancja',
  service: 'Serwis',
  noServiceOrders: 'Brak zleceń serwisowych',
  previous: 'Poprzednia',
  next: 'Następna',
  showing: 'Wyświetlanie',
  of: 'z',
  serviceOrdersText: 'zleceń serwisowych',
  viewDetails: 'Zobacz Szczegóły',
  edit: 'Edytuj',
  customer: 'Klient',
  device: 'Urządzenie',
  notScheduled: 'Nie zaplanowane',
};

const statusTranslations = {
  PENDING: translations.pending,
  SCHEDULED: translations.scheduled,
  IN_PROGRESS: translations.inProgress,
  WAITING_PARTS: translations.waitingParts,
  QUALITY_CHECK: translations.qualityCheck,
  COMPLETED: translations.completed,
  CANCELLED: translations.cancelled,
  BILLED: translations.billed,
};

const priorityTranslations = {
  LOW: translations.low,
  MEDIUM: translations.medium,
  HIGH: translations.high,
  URGENT: translations.urgent,
  EMERGENCY: translations.emergency,
};

const typeTranslations = {
  MAINTENANCE: translations.maintenance,
  REPAIR: translations.repair,
  INSTALLATION: translations.installation,
  INSPECTION: translations.inspection,
  EMERGENCY: translations.emergency,
  WARRANTY: translations.warranty,
  SERVICE: translations.service,
};

export default function ServiceOrdersIndexPage() {
  const { serviceOrders, totalCount, totalPages, currentPage, error } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.get("search") || "");

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const newParams = new URLSearchParams(searchParams);
    if (searchQuery) {
      newParams.set("search", searchQuery);
    } else {
      newParams.delete("search");
    }
    newParams.set("page", "1"); // Reset to first page on new search
    setSearchParams(newParams);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("page", newPage.toString());
    setSearchParams(newParams);
  };

  // Handle status filter
  const handleStatusFilter = (status: string | null) => {
    const newParams = new URLSearchParams(searchParams);
    if (status) {
      newParams.set("status", status);
    } else {
      newParams.delete("status");
    }
    newParams.set("page", "1"); // Reset to first page on filter change
    setSearchParams(newParams);
  };

  // Handle priority filter
  const handlePriorityFilter = (priority: string | null) => {
    const newParams = new URLSearchParams(searchParams);
    if (priority) {
      newParams.set("priority", priority);
    } else {
      newParams.delete("priority");
    }
    newParams.set("page", "1"); // Reset to first page on filter change
    setSearchParams(newParams);
  };

  // Handle type filter
  const handleTypeFilter = (type: string | null) => {
    const newParams = new URLSearchParams(searchParams);
    if (type) {
      newParams.set("type", type);
    } else {
      newParams.delete("type");
    }
    newParams.set("page", "1"); // Reset to first page on filter change
    setSearchParams(newParams);
  };

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">{translations.serviceOrders}</h1>
        <Link to="/service-orders/new">
          <Button>{translations.createServiceOrder}</Button>
        </Link>
      </div>

      {/* Search form */}
      <form onSubmit={handleSearch} className="mb-6">
        <div className="flex gap-2">
          <div className="flex-1">
            <Input
              type="text"
              placeholder={translations.searchPlaceholder}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button type="submit">{translations.search}</Button>
        </div>
      </form>

      {/* Status filters */}
      <div className="mb-4">
        <Label className="mb-2 block">{translations.status}</Label>
        <div className="flex flex-wrap gap-2">
          <Button
            variant={!searchParams.get("status") ? "default" : "outline"}
            size="sm"
            onClick={() => handleStatusFilter(null)}
          >
            {translations.all}
          </Button>
          {["PENDING", "SCHEDULED", "IN_PROGRESS", "WAITING_PARTS", "QUALITY_CHECK", "COMPLETED", "CANCELLED", "BILLED"].map((status) => (
            <Button
              key={status}
              variant={searchParams.get("status") === status ? "default" : "outline"}
              size="sm"
              onClick={() => handleStatusFilter(status)}
            >
              {statusTranslations[status as keyof typeof statusTranslations] || status}
            </Button>
          ))}
        </div>
      </div>

      {/* Priority filters */}
      <div className="mb-4">
        <Label className="mb-2 block">{translations.priority}</Label>
        <div className="flex flex-wrap gap-2">
          <Button
            variant={!searchParams.get("priority") ? "default" : "outline"}
            size="sm"
            onClick={() => handlePriorityFilter(null)}
          >
            {translations.all}
          </Button>
          {["LOW", "MEDIUM", "HIGH", "URGENT", "EMERGENCY"].map((priority) => (
            <Button
              key={priority}
              variant={searchParams.get("priority") === priority ? "default" : "outline"}
              size="sm"
              onClick={() => handlePriorityFilter(priority)}
            >
              {priorityTranslations[priority as keyof typeof priorityTranslations] || priority}
            </Button>
          ))}
        </div>
      </div>

      {/* Type filters */}
      <div className="mb-6">
        <Label className="mb-2 block">{translations.type}</Label>
        <div className="flex flex-wrap gap-2">
          <Button
            variant={!searchParams.get("type") ? "default" : "outline"}
            size="sm"
            onClick={() => handleTypeFilter(null)}
          >
            {translations.all}
          </Button>
          {["MAINTENANCE", "REPAIR", "INSTALLATION", "INSPECTION", "EMERGENCY", "WARRANTY", "SERVICE"].map((type) => (
            <Button
              key={type}
              variant={searchParams.get("type") === type ? "default" : "outline"}
              size="sm"
              onClick={() => handleTypeFilter(type)}
            >
              {typeTranslations[type as keyof typeof typeTranslations] || type}
            </Button>
          ))}
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Service Orders list */}
      <div className="space-y-4">
        {serviceOrders.length > 0 ? (
          serviceOrders.map((serviceOrder) => (
            <ServiceOrderCard key={serviceOrder.id} serviceOrder={serviceOrder} />
          ))
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">{translations.noServiceOrders}</p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              {translations.previous}
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={page === currentPage ? "default" : "outline"}
                  onClick={() => handlePageChange(page)}
                  className="w-10"
                >
                  {page}
                </Button>
              ))}
            </div>

            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              {translations.next}
            </Button>
          </div>
        </div>
      )}

      {/* Summary */}
      <div className="mt-6 text-center text-gray-500">
        {translations.showing} {serviceOrders.length} {translations.of} {totalCount} {translations.serviceOrdersText}
      </div>
    </div>
  );
}

function ServiceOrderCard({ serviceOrder }: { serviceOrder: ServiceOrder }) {
  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return translations.notScheduled;
    return new Date(dateString).toLocaleDateString('pl-PL');
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>
              <Link to={`/service-orders/${serviceOrder.id}`} className="hover:underline">
                {serviceOrder.title}
              </Link>
            </CardTitle>
            <CardDescription>
              {formatDate(serviceOrder.scheduledDate)}
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <span className={`px-2 py-1 rounded text-xs ${getStatusColor(serviceOrder.status)}`}>
              {statusTranslations[serviceOrder.status as keyof typeof statusTranslations] || serviceOrder.status}
            </span>
            <span className={`px-2 py-1 rounded text-xs ${getPriorityColor(serviceOrder.priority)}`}>
              {priorityTranslations[serviceOrder.priority as keyof typeof priorityTranslations] || serviceOrder.priority}
            </span>
            <span className={`px-2 py-1 rounded text-xs ${getTypeColor(serviceOrder.type)}`}>
              {typeTranslations[serviceOrder.type as keyof typeof typeTranslations] || serviceOrder.type}
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {serviceOrder.description && (
            <p className="text-sm text-gray-600 line-clamp-2">{serviceOrder.description}</p>
          )}

          <div className="flex flex-wrap gap-4 mt-2">
            {serviceOrder.customer && (
              <div>
                <Label className="text-xs">{translations.customer}</Label>
                <p className="text-sm">
                  <Link to={`/customers/${serviceOrder.customer.id}`} className="text-blue-500 hover:underline">
                    {serviceOrder.customer.name}
                  </Link>
                </p>
              </div>
            )}

            {serviceOrder.device && (
              <div>
                <Label className="text-xs">{translations.device}</Label>
                <p className="text-sm">
                  <Link to={`/devices/${serviceOrder.device.id}`} className="text-blue-500 hover:underline">
                    {serviceOrder.device.name}
                  </Link>
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link to={`/service-orders/${serviceOrder.id}`}>
          <Button variant="outline">{translations.viewDetails}</Button>
        </Link>
        <Link to={`/service-orders/${serviceOrder.id}/edit`}>
          <Button variant="outline">{translations.edit}</Button>
        </Link>
      </CardFooter>
    </Card>
  );
}

// Helper function to get status color
function getStatusColor(status: string) {
  switch (status.toUpperCase()) {
    case "PENDING":
      return "bg-yellow-100 text-yellow-800";
    case "IN_PROGRESS":
      return "bg-blue-100 text-blue-800";
    case "COMPLETED":
      return "bg-green-100 text-green-800";
    case "CANCELLED":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Helper function to get priority color
function getPriorityColor(priority: string) {
  switch (priority.toUpperCase()) {
    case "LOW":
      return "bg-green-100 text-green-800";
    case "MEDIUM":
      return "bg-blue-100 text-blue-800";
    case "HIGH":
      return "bg-orange-100 text-orange-800";
    case "URGENT":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Helper function to get type color
function getTypeColor(type: string) {
  switch (type?.toUpperCase()) {
    case "SERVICE":
      return "bg-blue-100 text-blue-800";
    case "INSTALLATION":
      return "bg-green-100 text-green-800";
    case "INSPECTION":
      return "bg-yellow-100 text-yellow-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}
